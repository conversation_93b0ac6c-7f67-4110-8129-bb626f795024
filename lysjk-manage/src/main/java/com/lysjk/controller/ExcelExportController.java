package com.lysjk.controller;

import com.lysjk.service.ExcelExportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Excel导出控制器
 * 提供水质监测数据的Excel导出功能
 */
@Slf4j
@RestController
@RequestMapping("/api/excel/export")
public class ExcelExportController {

    @Autowired
    private ExcelExportService excelExportService;

    private static final DateTimeFormatter FILENAME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");

    /**
     * 导出完整的Excel文件
     * 包含所有7个工作表：主表、监测点信息表、环境参数表、水质参数表、反射率光谱表、测量记录表、影像信息表
     * 
     * @param regionIds 地物ID列表，可选参数，如果不提供则导出所有数据
     * @return Excel文件
     */
    @GetMapping("/complete")
    public ResponseEntity<Resource> exportCompleteExcel(@RequestParam(required = false) List<Integer> regionIds) {
        log.info("开始导出完整Excel文件，地物ID列表: {}", regionIds);
        
        try {
            Resource resource = excelExportService.exportCompleteExcel(regionIds);
            
            String filename = generateFilename("水质监测数据完整导出");
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("导出完整Excel文件失败", e);
            throw new RuntimeException("导出Excel文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出地物信息主表
     * 
     * @param regionIds 地物ID列表，可选参数
     * @return Excel文件
     */
    @GetMapping("/region-info")
    public ResponseEntity<Resource> exportRegionInfoMain(@RequestParam(required = false) List<Integer> regionIds) {
        log.info("开始导出地物信息主表，地物ID列表: {}", regionIds);
        
        try {
            Resource resource = excelExportService.exportRegionInfoMain(regionIds);
            
            String filename = generateFilename("地物信息主表");
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("导出地物信息主表失败", e);
            throw new RuntimeException("导出地物信息主表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出监测点信息表
     * 
     * @param regionIds 地物ID列表，可选参数
     * @return Excel文件
     */
    @GetMapping("/monitoring-point")
    public ResponseEntity<Resource> exportMonitoringPoint(@RequestParam(required = false) List<Integer> regionIds) {
        log.info("开始导出监测点信息表，地物ID列表: {}", regionIds);
        
        try {
            Resource resource = excelExportService.exportMonitoringPoint(regionIds);
            
            String filename = generateFilename("监测点信息表");
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("导出监测点信息表失败", e);
            throw new RuntimeException("导出监测点信息表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出环境参数表
     * 
     * @param regionIds 地物ID列表，可选参数
     * @return Excel文件
     */
    @GetMapping("/water-environment")
    public ResponseEntity<Resource> exportWaterEnvironment(@RequestParam(required = false) List<Integer> regionIds) {
        log.info("开始导出环境参数表，地物ID列表: {}", regionIds);
        
        try {
            Resource resource = excelExportService.exportWaterEnvironment(regionIds);
            
            String filename = generateFilename("环境参数表");
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("导出环境参数表失败", e);
            throw new RuntimeException("导出环境参数表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出水质参数表
     * 
     * @param regionIds 地物ID列表，可选参数
     * @return Excel文件
     */
    @GetMapping("/water-quality")
    public ResponseEntity<Resource> exportWaterQuality(@RequestParam(required = false) List<Integer> regionIds) {
        log.info("开始导出水质参数表，地物ID列表: {}", regionIds);
        
        try {
            Resource resource = excelExportService.exportWaterQuality(regionIds);
            
            String filename = generateFilename("水质参数表");
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("导出水质参数表失败", e);
            throw new RuntimeException("导出水质参数表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出反射率光谱表
     * 
     * @param regionIds 地物ID列表，可选参数
     * @return Excel文件
     */
    @GetMapping("/water-spectrum")
    public ResponseEntity<Resource> exportWaterSpectrum(@RequestParam(required = false) List<Integer> regionIds) {
        log.info("开始导出反射率光谱表，地物ID列表: {}", regionIds);
        
        try {
            Resource resource = excelExportService.exportWaterSpectrum(regionIds);
            
            String filename = generateFilename("反射率光谱表");
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("导出反射率光谱表失败", e);
            throw new RuntimeException("导出反射率光谱表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出测量记录表
     * 
     * @param regionIds 地物ID列表，可选参数
     * @return Excel文件
     */
    @GetMapping("/monitoring-record")
    public ResponseEntity<Resource> exportMonitoringRecord(@RequestParam(required = false) List<Integer> regionIds) {
        log.info("开始导出测量记录表，地物ID列表: {}", regionIds);
        
        try {
            Resource resource = excelExportService.exportMonitoringRecord(regionIds);
            
            String filename = generateFilename("测量记录表");
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("导出测量记录表失败", e);
            throw new RuntimeException("导出测量记录表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出影像信息表
     * 
     * @param regionIds 地物ID列表，可选参数
     * @return Excel文件
     */
    @GetMapping("/image")
    public ResponseEntity<Resource> exportImage(@RequestParam(required = false) List<Integer> regionIds) {
        log.info("开始导出影像信息表，地物ID列表: {}", regionIds);
        
        try {
            Resource resource = excelExportService.exportImage(regionIds);
            
            String filename = generateFilename("影像信息表");
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("导出影像信息表失败", e);
            throw new RuntimeException("导出影像信息表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成文件名
     * 
     * @param prefix 文件名前缀
     * @return 完整的文件名
     */
    private String generateFilename(String prefix) {
        String timestamp = LocalDateTime.now().format(FILENAME_FORMATTER);
        return prefix + "_" + timestamp + ".xlsx";
    }
}
