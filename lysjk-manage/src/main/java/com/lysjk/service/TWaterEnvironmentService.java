package com.lysjk.service;

import com.lysjk.dto.TWaterEnvironmentPageQueryDTO;
import com.lysjk.entity.TWaterEnvironment;
import com.lysjk.result.PageResult;

import java.util.List;

/**
 * 水域环境信息服务接口
 */
public interface TWaterEnvironmentService {

    /**
     * 新增水域环境信息
     * @param waterEnvironment 水域环境信息
     */
    void save(TWaterEnvironment waterEnvironment);

    /**
     * 批量删除水域环境信息
     * @param ids ID列表
     */
    void deleteBatch(List<Integer> ids);

    /**
     * 更新水域环境信息
     * @param waterEnvironment 水域环境信息
     */
    void update(TWaterEnvironment waterEnvironment);

    /**
     * 分页查询水域环境信息
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    PageResult selectPage(TWaterEnvironmentPageQueryDTO pageQueryDTO);

    /**
     * 批量新增水域环境信息
     * @param waterEnvironmentList 水域环境信息列表
     */
    void batchInsert(List<TWaterEnvironment> waterEnvironmentList);

    /**
     * 根据地物ID列表查询水域环境信息
     * @param regionIds 地物ID列表
     * @return 水域环境信息列表
     */
    List<TWaterEnvironment> selectByRegionIds(List<Integer> regionIds);

    /**
     * 查询所有水域环境信息
     * @return 水域环境信息列表
     */
    List<TWaterEnvironment> selectAll();
}
