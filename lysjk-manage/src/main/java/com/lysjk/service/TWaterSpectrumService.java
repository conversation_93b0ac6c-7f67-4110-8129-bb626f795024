package com.lysjk.service;

import com.lysjk.dto.TWaterSpectrumPageQueryDTO;
import com.lysjk.entity.TWaterSpectrum;
import com.lysjk.result.PageResult;
import org.springframework.web.multipart.MultipartFile;


import java.util.List;

/**
 * 波谱信息服务接口
 */
public interface TWaterSpectrumService {

    /**
     * 新增波谱信息
     * @param waterSpectrum 波谱信息
     */
    void save(TWaterSpectrum waterSpectrum);

    /**
     * 批量删除波谱信息
     * @param ids ID列表
     */
    void deleteBatch(List<Integer> ids);

    /**
     * 更新波谱信息
     * @param waterSpectrum 波谱信息
     */
    void update(TWaterSpectrum waterSpectrum);

    /**
     * 分页查询波谱信息
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    PageResult selectPage(TWaterSpectrumPageQueryDTO pageQueryDTO);

    /**
     * 从CSV文件导入波谱数据
     * @param file CSV文件
     */
    void importSpectrumFromCsv(MultipartFile file);

    /**
     * 批量新增波谱信息
     * @param waterSpectrumList 波谱信息列表
     */
    void batchInsert(List<TWaterSpectrum> waterSpectrumList);
}