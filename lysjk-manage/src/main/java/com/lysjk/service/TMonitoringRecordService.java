package com.lysjk.service;

import com.lysjk.dto.TMonitoringRecordPageQueryDTO;
import com.lysjk.entity.TMonitoringRecord;
import com.lysjk.result.PageResult;

import java.util.List;

/**
 * 测量记录服务接口
 */
public interface TMonitoringRecordService {

    /**
     * 新增测量记录
     * @param monitoringRecord 测量记录信息
     */
    void save(TMonitoringRecord monitoringRecord);

    /**
     * 批量删除测量记录
     * @param ids ID列表
     */
    void deleteBatch(List<Integer> ids);

    /**
     * 更新测量记录
     * @param monitoringRecord 测量记录信息
     */
    void update(TMonitoringRecord monitoringRecord);

    /**
     * 分页查询测量记录
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    PageResult selectPage(TMonitoringRecordPageQueryDTO pageQueryDTO);

    /**
     * 批量新增测量记录
     * @param monitoringRecordList 测量记录列表
     */
    void batchInsert(List<TMonitoringRecord> monitoringRecordList);

    /**
     * 根据地物ID列表查询测量记录
     * @param regionIds 地物ID列表
     * @return 测量记录列表
     */
    List<TMonitoringRecord> selectByRegionIds(List<Integer> regionIds);

    /**
     * 查询所有测量记录
     * @return 测量记录列表
     */
    List<TMonitoringRecord> selectAll();
}
