package com.lysjk.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lysjk.constant.RoleConstant;
import com.lysjk.dto.TMonitoringPointPageQueryDTO;
import com.lysjk.entity.TMonitoringPoint;
import com.lysjk.exception.AuthenticationException;
import com.lysjk.exception.BusinessException;
import com.lysjk.exception.ValidationException;
import com.lysjk.mapper.TMonitoringPointMapper;
import com.lysjk.result.PageResult;
import com.lysjk.service.TMonitoringPointService;
import com.lysjk.utils.ThreadLocalUtil;
import com.lysjk.vo.excel.TMonitoringPointExcelVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 监测点信息服务实现类
 */
@Slf4j
@Service
@Transactional
public class TMonitoringPointServiceImpl implements TMonitoringPointService {

    @Autowired
    private TMonitoringPointMapper tMonitoringPointMapper;

    /**
     * 新增监测点信息
     * @param monitoringPoint 监测点信息
     */
    @Override
    public void save(TMonitoringPoint monitoringPoint) {
        if (monitoringPoint == null) {
            throw new ValidationException.ParameterNullException("监测点信息");
        }

        // 检查监测点编码唯一性
        if (monitoringPoint.getCode() != null && !monitoringPoint.getCode().trim().isEmpty()) {
            checkCodeUnique(monitoringPoint.getCode());
        }

        log.info("新增监测点信息: code={}, name={}", monitoringPoint.getCode(), monitoringPoint.getName());

        int result = tMonitoringPointMapper.insertSelective(monitoringPoint);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("新增监测点信息");
        }
    }

    /**
     * 批量删除监测点信息
     * @param ids ID列表
     */
    @Override
    public void deleteBatch(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new ValidationException.ParameterNullException("删除ID列表");
        }

        // 权限验证
        Map<String, Object> claims = ThreadLocalUtil.get();
        String role = (String) claims.get(RoleConstant.ROLE);
        if (!RoleConstant.ADMIN.equals(role)) {
            throw new AuthenticationException.InsufficientPermissionException("删除监测点信息");
        }

        log.info("批量删除监测点信息: {}", ids);

        int result = tMonitoringPointMapper.deleteByIds(ids);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("删除监测点信息");
        }
    }

    /**
     * 更新监测点信息
     * @param monitoringPoint 监测点信息
     */
    @Override
    public void update(TMonitoringPoint monitoringPoint) {
        if (monitoringPoint == null) {
            throw new ValidationException.ParameterNullException("监测点信息");
        }

        if (monitoringPoint.getId() == null) {
            throw new ValidationException.ParameterNullException("监测点信息ID");
        }

        // 检查监测点编码唯一性（排除当前记录）
        if (monitoringPoint.getCode() != null && !monitoringPoint.getCode().trim().isEmpty()) {
            checkCodeUnique(monitoringPoint.getCode());
        }

        log.info("更新监测点信息: id={}, code={}, name={}",
                monitoringPoint.getId(), monitoringPoint.getCode(), monitoringPoint.getName());

        int result = tMonitoringPointMapper.updateBusinessFields(monitoringPoint);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("更新监测点信息");
        }
    }

    @Override
    public TMonitoringPoint selectByPrimaryKey(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("监测点ID不能为空");
        }
        log.info("根据ID查询监测点信息: {}", id);
        return tMonitoringPointMapper.selectByPrimaryKey(id);
    }

    /**
     * 分页条件查询监测点信息
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    @Override
    public PageResult selectPage(TMonitoringPointPageQueryDTO pageQueryDTO) {
        if (pageQueryDTO == null) {
            throw new ValidationException.ParameterNullException("分页查询条件");
        }

        log.info("分页查询监测点信息: page={}, pageSize={}, name={}, code={}",
                pageQueryDTO.getPage(), pageQueryDTO.getPageSize(),
                pageQueryDTO.getName(), pageQueryDTO.getCode());

        // 开启分页
        PageHelper.startPage(pageQueryDTO.getPage(), pageQueryDTO.getPageSize());

        // 执行查询
        Page<TMonitoringPoint> page = tMonitoringPointMapper.pageQuery(pageQueryDTO);

        // 封装结果
        long total = page.getTotal();
        List<TMonitoringPoint> result = page.getResult();

        log.info("分页查询完成，共查询到{}条记录", total);
        return new PageResult(total, result);
    }

    @Override
    public List<TMonitoringPoint> selectByRegionId(Integer regionId) {
        log.info("根据地物ID查询监测点: {}", regionId);
        return tMonitoringPointMapper.selectByRegionId(regionId);
    }

    /**
     * 检查监测点编码唯一性（简化版本）
     * @param code 监测点编码
     */
    @Override
    public void checkCodeUnique(String code) {
        if (code == null || code.trim().isEmpty()) {
            throw new IllegalArgumentException("监测点编码不能为空");
        }

        log.info("检查监测点编码唯一性 - 编码:{}", code);

        TMonitoringPoint existing = tMonitoringPointMapper.selectByCode(code.trim());
        if (existing != null) {
            log.warn("监测点编码已存在 - 编码:{}, 已存在ID:{}", code, existing.getId());
            throw new ValidationException.DuplicateDataException("监测点编码", code);
        }

        log.info("监测点编码唯一性检查通过 - 编码:{}", code);
    }

    /**
     * excel表格中可以使用的方法
     * @param code
     * @return
     */
    public TMonitoringPointExcelVO selectExcelByCode(String code){
        TMonitoringPointExcelVO tMonitoringPointExcelVO = new TMonitoringPointExcelVO();
        TMonitoringPoint tMonitoringPoint = tMonitoringPointMapper.selectByCode(code);
        if(tMonitoringPoint == null){
            throw new IllegalArgumentException("请检查Excel填写的监测点编码是否存在且合理");
        }
        tMonitoringPointExcelVO.setPointId(tMonitoringPoint.getId());
        tMonitoringPointExcelVO.setRegionId(tMonitoringPoint.getRegionId());
        return tMonitoringPointExcelVO;
    }

    /**
     * 批量新增监测点信息
     * @param monitoringPointList 监测点信息列表
     */
    @Override
    public void batchInsert(List<TMonitoringPoint> monitoringPointList) {
        if (monitoringPointList == null || monitoringPointList.isEmpty()) {
            throw new ValidationException.ParameterNullException("监测点信息列表");
        }

        log.info("批量新增监测点信息，数量: {}", monitoringPointList.size());

        int result = tMonitoringPointMapper.batchInsert(monitoringPointList);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("批量新增监测点信息");
        }

        log.info("批量新增监测点信息成功，实际插入数量: {}", result);
    }
}
