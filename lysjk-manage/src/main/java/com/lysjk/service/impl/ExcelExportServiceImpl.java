package com.lysjk.service.impl;

import com.lysjk.entity.*;
import com.lysjk.service.*;
import com.lysjk.utils.CoordinateUtil;
import com.lysjk.utils.GeometryUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * Excel导出服务实现类
 * 实现完整的Excel文件导出功能，支持导出水质监测数据到Excel文件中
 * 导出格式与导入模板完全一致，包含7个工作表
 */
@Slf4j
@Service
public class ExcelExportServiceImpl implements ExcelExportService {

    @Autowired
    private TRegionInfoService regionInfoService;

    @Autowired
    private TMonitoringPointService monitoringPointService;

    @Autowired
    private TWaterEnvironmentService waterEnvironmentService;

    @Autowired
    private TWaterQualityService waterQualityService;

    @Autowired
    private TWaterSpectrumService waterSpectrumService;

    @Autowired
    private TMonitoringRecordService monitoringRecordService;

    @Autowired
    private TImageService imageService;

    // Excel工作表名称常量
    private static final String SHEET_REGION_INFO = "主表";
    private static final String SHEET_MONITORING_POINT = "监测点信息表";
    private static final String SHEET_WATER_ENVIRONMENT = "环境参数表";
    private static final String SHEET_WATER_QUALITY = "水质参数表";
    private static final String SHEET_WATER_SPECTRUM = "反射率光谱表";
    private static final String SHEET_MONITORING_RECORD = "测量记录表";
    private static final String SHEET_IMAGE = "影像信息表";

    // 日期时间格式化器
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    @Override
    public Resource exportCompleteExcel(List<Integer> regionIds) {
        log.info("开始导出完整Excel文件，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            // 创建所有工作表
            createRegionInfoMainSheet(workbook, regionIds);
            createMonitoringPointSheet(workbook, regionIds);
            createWaterEnvironmentSheet(workbook, regionIds);
            createWaterQualitySheet(workbook, regionIds);
            createWaterSpectrumSheet(workbook, regionIds);
            createMonitoringRecordSheet(workbook, regionIds);
            createImageSheet(workbook, regionIds);

            // 将工作簿写入字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("完整Excel文件导出成功，文件大小: {} bytes", outputStream.size());
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出完整Excel文件失败", e);
            throw new RuntimeException("导出Excel文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportRegionInfoMain(List<Integer> regionIds) {
        log.info("开始导出地物信息主表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createRegionInfoMainSheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("地物信息主表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出地物信息主表失败", e);
            throw new RuntimeException("导出地物信息主表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportMonitoringPoint(List<Integer> regionIds) {
        log.info("开始导出监测点信息表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createMonitoringPointSheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("监测点信息表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出监测点信息表失败", e);
            throw new RuntimeException("导出监测点信息表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportWaterEnvironment(List<Integer> regionIds) {
        log.info("开始导出环境参数表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createWaterEnvironmentSheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("环境参数表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出环境参数表失败", e);
            throw new RuntimeException("导出环境参数表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportWaterQuality(List<Integer> regionIds) {
        log.info("开始导出水质参数表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createWaterQualitySheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("水质参数表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出水质参数表失败", e);
            throw new RuntimeException("导出水质参数表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportWaterSpectrum(List<Integer> regionIds) {
        log.info("开始导出反射率光谱表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createWaterSpectrumSheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("反射率光谱表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出反射率光谱表失败", e);
            throw new RuntimeException("导出反射率光谱表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportMonitoringRecord(List<Integer> regionIds) {
        log.info("开始导出测量记录表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createMonitoringRecordSheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("测量记录表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出测量记录表失败", e);
            throw new RuntimeException("导出测量记录表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportImage(List<Integer> regionIds) {
        log.info("开始导出影像信息表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createImageSheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("影像信息表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出影像信息表失败", e);
            throw new RuntimeException("导出影像信息表失败: " + e.getMessage(), e);
        }
    }
