package com.lysjk.service.impl;

import com.lysjk.entity.*;
import com.lysjk.service.*;
import com.lysjk.utils.CoordinateUtil;
import com.lysjk.utils.GeometryUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * Excel导出服务实现类
 * 实现完整的Excel文件导出功能，支持导出水质监测数据到Excel文件中
 * 导出格式与导入模板完全一致，包含7个工作表
 */
@Slf4j
@Service
public class ExcelExportServiceImpl implements ExcelExportService {

    @Autowired
    private TRegionInfoService regionInfoService;

    @Autowired
    private TMonitoringPointService monitoringPointService;

    @Autowired
    private TWaterEnvironmentService waterEnvironmentService;

    @Autowired
    private TWaterQualityService waterQualityService;

    @Autowired
    private TWaterSpectrumService waterSpectrumService;

    @Autowired
    private TMonitoringRecordService monitoringRecordService;

    @Autowired
    private TImageService imageService;

    // Excel工作表名称常量
    private static final String SHEET_REGION_INFO = "主表";
    private static final String SHEET_MONITORING_POINT = "监测点信息表";
    private static final String SHEET_WATER_ENVIRONMENT = "环境参数表";
    private static final String SHEET_WATER_QUALITY = "水质参数表";
    private static final String SHEET_WATER_SPECTRUM = "反射率光谱表";
    private static final String SHEET_MONITORING_RECORD = "测量记录表";
    private static final String SHEET_IMAGE = "影像信息表";

    // 日期时间格式化器
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    @Override
    public Resource exportCompleteExcel(List<Integer> regionIds) {
        log.info("开始导出完整Excel文件，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            // 创建所有工作表
            createRegionInfoMainSheet(workbook, regionIds);
            createMonitoringPointSheet(workbook, regionIds);
            createWaterEnvironmentSheet(workbook, regionIds);
            createWaterQualitySheet(workbook, regionIds);
            createWaterSpectrumSheet(workbook, regionIds);
            createMonitoringRecordSheet(workbook, regionIds);
            createImageSheet(workbook, regionIds);

            // 将工作簿写入字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("完整Excel文件导出成功，文件大小: {} bytes", outputStream.size());
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出完整Excel文件失败", e);
            throw new RuntimeException("导出Excel文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportRegionInfoMain(List<Integer> regionIds) {
        log.info("开始导出地物信息主表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createRegionInfoMainSheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("地物信息主表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出地物信息主表失败", e);
            throw new RuntimeException("导出地物信息主表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportMonitoringPoint(List<Integer> regionIds) {
        log.info("开始导出监测点信息表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createMonitoringPointSheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("监测点信息表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出监测点信息表失败", e);
            throw new RuntimeException("导出监测点信息表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportWaterEnvironment(List<Integer> regionIds) {
        log.info("开始导出环境参数表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createWaterEnvironmentSheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("环境参数表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出环境参数表失败", e);
            throw new RuntimeException("导出环境参数表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportWaterQuality(List<Integer> regionIds) {
        log.info("开始导出水质参数表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createWaterQualitySheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("水质参数表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出水质参数表失败", e);
            throw new RuntimeException("导出水质参数表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportWaterSpectrum(List<Integer> regionIds) {
        log.info("开始导出反射率光谱表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createWaterSpectrumSheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("反射率光谱表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出反射率光谱表失败", e);
            throw new RuntimeException("导出反射率光谱表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportMonitoringRecord(List<Integer> regionIds) {
        log.info("开始导出测量记录表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createMonitoringRecordSheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("测量记录表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出测量记录表失败", e);
            throw new RuntimeException("导出测量记录表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource exportImage(List<Integer> regionIds) {
        log.info("开始导出影像信息表，地物ID列表: {}", regionIds);

        try (Workbook workbook = new XSSFWorkbook()) {
            createImageSheet(workbook, regionIds);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("影像信息表导出成功");
            return new ByteArrayResource(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("导出影像信息表失败", e);
            throw new RuntimeException("导出影像信息表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建地物信息主表工作表
     */
    private void createRegionInfoMainSheet(Workbook workbook, List<Integer> regionIds) {
        Sheet sheet = workbook.createSheet(SHEET_REGION_INFO);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"序号", "地物编码", "采集时间", "采集地点", "采集方法", "完成单位", "备注"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            // 设置表头样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            cell.setCellStyle(headerStyle);
        }

        // 查询数据
        List<TRegionInfo> regionInfoList;
        if (regionIds != null && !regionIds.isEmpty()) {
            regionInfoList = regionInfoService.selectByIds(regionIds);
        } else {
            regionInfoList = regionInfoService.selectAll();
        }

        // 填充数据
        int rowIndex = 1;
        for (TRegionInfo regionInfo : regionInfoList) {
            Row row = sheet.createRow(rowIndex++);

            // 序号
            setCellValue(row.createCell(0), regionInfo.getNumber());

            // 地物编码
            setCellValue(row.createCell(1), regionInfo.getCode());

            // 采集时间（格式化为日期，时间部分为00:00）
            if (regionInfo.getSampleDt() != null) {
                setCellValue(row.createCell(2), regionInfo.getSampleDt().format(DATE_FORMATTER));
            } else {
                setCellValue(row.createCell(2), "");
            }

            // 采集地点
            setCellValue(row.createCell(3), regionInfo.getName());

            // 采集方法
            setCellValue(row.createCell(4), regionInfo.getMethod());

            // 完成单位
            setCellValue(row.createCell(5), regionInfo.getUnit());

            // 备注
            setCellValue(row.createCell(6), regionInfo.getRemark());
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        log.info("地物信息主表创建完成，共导出 {} 条数据", regionInfoList.size());
    }

    /**
     * 创建监测点信息表工作表
     */
    private void createMonitoringPointSheet(Workbook workbook, List<Integer> regionIds) {
        Sheet sheet = workbook.createSheet(SHEET_MONITORING_POINT);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"序号", "采样点号", "地点", "经度", "纬度"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            CellStyle headerStyle = createHeaderStyle(workbook);
            cell.setCellStyle(headerStyle);
        }

        // 查询数据
        List<TMonitoringPoint> monitoringPointList;
        if (regionIds != null && !regionIds.isEmpty()) {
            monitoringPointList = monitoringPointService.selectByRegionIds(regionIds);
        } else {
            monitoringPointList = monitoringPointService.selectAll();
        }

        // 填充数据
        int rowIndex = 1;
        int sequenceNumber = 1;
        for (TMonitoringPoint monitoringPoint : monitoringPointList) {
            Row row = sheet.createRow(rowIndex++);

            // 序号
            setCellValue(row.createCell(0), String.valueOf(sequenceNumber++));

            // 采样点号
            setCellValue(row.createCell(1), monitoringPoint.getCode());

            // 地点
            setCellValue(row.createCell(2), monitoringPoint.getName());

            // 经度和纬度（转换为度分秒格式）
            if (monitoringPoint.getLocation() != null) {
                Point location = monitoringPoint.getLocation();
                double longitude = GeometryUtil.getLongitude(location);
                double latitude = GeometryUtil.getLatitude(location);

                // 转换为度分秒格式
                String longitudeDms = CoordinateUtil.decimalToDms(longitude);
                String latitudeDms = CoordinateUtil.decimalToDms(latitude);

                setCellValue(row.createCell(3), longitudeDms);
                setCellValue(row.createCell(4), latitudeDms);
            } else {
                setCellValue(row.createCell(3), "");
                setCellValue(row.createCell(4), "");
            }
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        log.info("监测点信息表创建完成，共导出 {} 条数据", monitoringPointList.size());
    }

    /**
     * 创建环境参数表工作表
     */
    private void createWaterEnvironmentSheet(Workbook workbook, List<Integer> regionIds) {
        Sheet sheet = workbook.createSheet(SHEET_WATER_ENVIRONMENT);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"序号", "采样点号", "监测日期", "监测时间", "天气", "风向", "风速(m/s)",
                           "气温(℃)", "水温(℃)", "透明度(cm)", "水深(m)", "流速(m/s)", "流向", "备注"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            CellStyle headerStyle = createHeaderStyle(workbook);
            cell.setCellStyle(headerStyle);
        }

        // 查询数据
        List<TWaterEnvironment> waterEnvironmentList;
        if (regionIds != null && !regionIds.isEmpty()) {
            waterEnvironmentList = waterEnvironmentService.selectByRegionIds(regionIds);
        } else {
            waterEnvironmentList = waterEnvironmentService.selectAll();
        }

        // 填充数据
        int rowIndex = 1;
        int sequenceNumber = 1;
        for (TWaterEnvironment waterEnvironment : waterEnvironmentList) {
            Row row = sheet.createRow(rowIndex++);

            // 序号
            setCellValue(row.createCell(0), String.valueOf(sequenceNumber++));

            // 采样点号
            setCellValue(row.createCell(1), waterEnvironment.getPointCode());

            // 监测日期和监测时间（分离显示）
            if (waterEnvironment.getMonitoringTime() != null) {
                LocalDateTime monitoringTime = waterEnvironment.getMonitoringTime();
                setCellValue(row.createCell(2), monitoringTime.format(DATE_FORMATTER));
                setCellValue(row.createCell(3), monitoringTime.format(TIME_FORMATTER));
            } else {
                setCellValue(row.createCell(2), "");
                setCellValue(row.createCell(3), "");
            }

            // 天气
            setCellValue(row.createCell(4), waterEnvironment.getWeather());

            // 风向
            setCellValue(row.createCell(5), waterEnvironment.getWindDirection());

            // 风速
            setCellValue(row.createCell(6), formatDoubleValue(waterEnvironment.getWindSpeed()));

            // 气温
            setCellValue(row.createCell(7), formatDoubleValue(waterEnvironment.getAirTemperature()));

            // 水温
            setCellValue(row.createCell(8), formatDoubleValue(waterEnvironment.getWaterTemperature()));

            // 透明度
            setCellValue(row.createCell(9), formatDoubleValue(waterEnvironment.getTransparency()));

            // 水深
            setCellValue(row.createCell(10), formatDoubleValue(waterEnvironment.getWaterDepth()));

            // 流速
            setCellValue(row.createCell(11), formatDoubleValue(waterEnvironment.getFlowVelocity()));

            // 流向
            setCellValue(row.createCell(12), waterEnvironment.getFlowDirection());

            // 备注
            setCellValue(row.createCell(13), waterEnvironment.getRemark());
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        log.info("环境参数表创建完成，共导出 {} 条数据", waterEnvironmentList.size());
    }

    /**
     * 创建水质参数表工作表
     */
    private void createWaterQualitySheet(Workbook workbook, List<Integer> regionIds) {
        Sheet sheet = workbook.createSheet(SHEET_WATER_QUALITY);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"序号", "采样点号", "监测日期", "监测时间", "pH", "溶解氧(mg/L)", "高锰酸盐指数(mg/L)",
                           "化学需氧量(mg/L)", "五日生化需氧量(mg/L)", "氨氮(mg/L)", "总磷(mg/L)", "总氮(mg/L)",
                           "铜(mg/L)", "锌(mg/L)", "氟化物(mg/L)", "硒(mg/L)", "砷(mg/L)", "汞(mg/L)",
                           "镉(mg/L)", "铬(六价)(mg/L)", "铅(mg/L)", "氰化物(mg/L)", "挥发酚(mg/L)",
                           "石油类(mg/L)", "阴离子表面活性剂(mg/L)", "硫化物(mg/L)", "粪大肠菌群(个/L)", "备注"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            CellStyle headerStyle = createHeaderStyle(workbook);
            cell.setCellStyle(headerStyle);
        }

        // 查询数据
        List<TWaterQuality> waterQualityList;
        if (regionIds != null && !regionIds.isEmpty()) {
            waterQualityList = waterQualityService.selectByRegionIds(regionIds);
        } else {
            waterQualityList = waterQualityService.selectAll();
        }

        // 填充数据
        int rowIndex = 1;
        int sequenceNumber = 1;
        for (TWaterQuality waterQuality : waterQualityList) {
            Row row = sheet.createRow(rowIndex++);

            // 序号
            setCellValue(row.createCell(0), String.valueOf(sequenceNumber++));

            // 采样点号
            setCellValue(row.createCell(1), waterQuality.getPointCode());

            // 监测日期和监测时间（分离显示）
            if (waterQuality.getMonitoringTime() != null) {
                LocalDateTime monitoringTime = waterQuality.getMonitoringTime();
                setCellValue(row.createCell(2), monitoringTime.format(DATE_FORMATTER));
                setCellValue(row.createCell(3), monitoringTime.format(TIME_FORMATTER));
            } else {
                setCellValue(row.createCell(2), "");
                setCellValue(row.createCell(3), "");
            }

            // 各种水质参数
            setCellValue(row.createCell(4), formatDoubleValue(waterQuality.getPh()));
            setCellValue(row.createCell(5), formatDoubleValue(waterQuality.getDissolvedOxygen()));
            setCellValue(row.createCell(6), formatDoubleValue(waterQuality.getPermanganateIndex()));
            setCellValue(row.createCell(7), formatDoubleValue(waterQuality.getChemicalOxygenDemand()));
            setCellValue(row.createCell(8), formatDoubleValue(waterQuality.getBiochemicalOxygenDemand()));
            setCellValue(row.createCell(9), formatDoubleValue(waterQuality.getAmmoniumNitrogen()));
            setCellValue(row.createCell(10), formatDoubleValue(waterQuality.getTotalPhosphorus()));
            setCellValue(row.createCell(11), formatDoubleValue(waterQuality.getTotalNitrogen()));
            setCellValue(row.createCell(12), formatDoubleValue(waterQuality.getCopper()));
            setCellValue(row.createCell(13), formatDoubleValue(waterQuality.getZinc()));
            setCellValue(row.createCell(14), formatDoubleValue(waterQuality.getFluoride()));
            setCellValue(row.createCell(15), formatDoubleValue(waterQuality.getSelenium()));
            setCellValue(row.createCell(16), formatDoubleValue(waterQuality.getArsenic()));
            setCellValue(row.createCell(17), formatDoubleValue(waterQuality.getMercury()));
            setCellValue(row.createCell(18), formatDoubleValue(waterQuality.getCadmium()));
            setCellValue(row.createCell(19), formatDoubleValue(waterQuality.getHexavalentChromium()));
            setCellValue(row.createCell(20), formatDoubleValue(waterQuality.getLead()));
            setCellValue(row.createCell(21), formatDoubleValue(waterQuality.getCyanide()));
            setCellValue(row.createCell(22), formatDoubleValue(waterQuality.getVolatilePhenol()));
            setCellValue(row.createCell(23), formatDoubleValue(waterQuality.getPetroleum()));
            setCellValue(row.createCell(24), formatDoubleValue(waterQuality.getAnionicSurfactant()));
            setCellValue(row.createCell(25), formatDoubleValue(waterQuality.getSulfide()));
            setCellValue(row.createCell(26), formatDoubleValue(waterQuality.getFecalColiform()));

            // 备注
            setCellValue(row.createCell(27), waterQuality.getRemark());
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        log.info("水质参数表创建完成，共导出 {} 条数据", waterQualityList.size());
    }

    /**
     * 创建反射率光谱表工作表
     */
    private void createWaterSpectrumSheet(Workbook workbook, List<Integer> regionIds) {
        Sheet sheet = workbook.createSheet(SHEET_WATER_SPECTRUM);

        // 查询数据以确定波长列
        List<TWaterSpectrum> waterSpectrumList;
        if (regionIds != null && !regionIds.isEmpty()) {
            waterSpectrumList = waterSpectrumService.selectByRegionIds(regionIds);
        } else {
            waterSpectrumList = waterSpectrumService.selectAll();
        }

        if (waterSpectrumList.isEmpty()) {
            // 如果没有数据，创建基本表头
            Row headerRow = sheet.createRow(0);
            String[] basicHeaders = {"序号", "采样点号", "监测日期", "监测时间", "备注"};
            for (int i = 0; i < basicHeaders.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(basicHeaders[i]);
                CellStyle headerStyle = createHeaderStyle(workbook);
                cell.setCellStyle(headerStyle);
            }
            log.info("反射率光谱表创建完成，无数据");
            return;
        }

        // 解析第一条记录的光谱数据以获取波长列
        String[] wavelengthColumns = parseWavelengthColumns(waterSpectrumList.get(0).getSpectrum());

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] basicHeaders = {"序号", "采样点号", "监测日期", "监测时间"};
        int colIndex = 0;

        // 基本列
        for (String header : basicHeaders) {
            Cell cell = headerRow.createCell(colIndex++);
            cell.setCellValue(header);
            CellStyle headerStyle = createHeaderStyle(workbook);
            cell.setCellStyle(headerStyle);
        }

        // 波长列
        for (String wavelength : wavelengthColumns) {
            Cell cell = headerRow.createCell(colIndex++);
            cell.setCellValue(wavelength + "nm");
            CellStyle headerStyle = createHeaderStyle(workbook);
            cell.setCellStyle(headerStyle);
        }

        // 备注列
        Cell remarkCell = headerRow.createCell(colIndex);
        remarkCell.setCellValue("备注");
        CellStyle headerStyle = createHeaderStyle(workbook);
        remarkCell.setCellStyle(headerStyle);

        // 填充数据
        int rowIndex = 1;
        int sequenceNumber = 1;
        for (TWaterSpectrum waterSpectrum : waterSpectrumList) {
            Row row = sheet.createRow(rowIndex++);
            colIndex = 0;

            // 序号
            setCellValue(row.createCell(colIndex++), String.valueOf(sequenceNumber++));

            // 采样点号
            setCellValue(row.createCell(colIndex++), waterSpectrum.getPointCode());

            // 监测日期和监测时间（分离显示）
            if (waterSpectrum.getMonitoringTime() != null) {
                LocalDateTime monitoringTime = waterSpectrum.getMonitoringTime();
                setCellValue(row.createCell(colIndex++), monitoringTime.format(DATE_FORMATTER));
                setCellValue(row.createCell(colIndex++), monitoringTime.format(TIME_FORMATTER));
            } else {
                setCellValue(row.createCell(colIndex++), "");
                setCellValue(row.createCell(colIndex++), "");
            }

            // 光谱数据
            Map<String, Double> spectrumData = parseSpectrumData(waterSpectrum.getSpectrum());
            for (String wavelength : wavelengthColumns) {
                Double value = spectrumData.get(wavelength);
                setCellValue(row.createCell(colIndex++), formatDoubleValue(value));
            }

            // 备注
            setCellValue(row.createCell(colIndex), waterSpectrum.getRemark());
        }

        // 自动调整列宽
        for (int i = 0; i <= colIndex; i++) {
            sheet.autoSizeColumn(i);
        }

        log.info("反射率光谱表创建完成，共导出 {} 条数据", waterSpectrumList.size());
    }

    /**
     * 创建测量记录表工作表
     */
    private void createMonitoringRecordSheet(Workbook workbook, List<Integer> regionIds) {
        Sheet sheet = workbook.createSheet(SHEET_MONITORING_RECORD);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"序号", "采样点号", "监测日期", "监测时间", "测量方法", "测量设备",
                           "设备型号", "测量人员", "数据质量", "备注"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            CellStyle headerStyle = createHeaderStyle(workbook);
            cell.setCellStyle(headerStyle);
        }

        // 查询数据
        List<TMonitoringRecord> monitoringRecordList;
        if (regionIds != null && !regionIds.isEmpty()) {
            monitoringRecordList = monitoringRecordService.selectByRegionIds(regionIds);
        } else {
            monitoringRecordList = monitoringRecordService.selectAll();
        }

        // 填充数据
        int rowIndex = 1;
        int sequenceNumber = 1;
        for (TMonitoringRecord monitoringRecord : monitoringRecordList) {
            Row row = sheet.createRow(rowIndex++);

            // 序号
            setCellValue(row.createCell(0), String.valueOf(sequenceNumber++));

            // 采样点号
            setCellValue(row.createCell(1), monitoringRecord.getPointCode());

            // 监测日期和监测时间（分离显示）
            if (monitoringRecord.getMonitoringTime() != null) {
                LocalDateTime monitoringTime = monitoringRecord.getMonitoringTime();
                setCellValue(row.createCell(2), monitoringTime.format(DATE_FORMATTER));
                setCellValue(row.createCell(3), monitoringTime.format(TIME_FORMATTER));
            } else {
                setCellValue(row.createCell(2), "");
                setCellValue(row.createCell(3), "");
            }

            // 测量方法
            setCellValue(row.createCell(4), monitoringRecord.getMeasurementMethod());

            // 测量设备
            setCellValue(row.createCell(5), monitoringRecord.getMeasurementEquipment());

            // 设备型号
            setCellValue(row.createCell(6), monitoringRecord.getEquipmentModel());

            // 测量人员
            setCellValue(row.createCell(7), monitoringRecord.getMeasurementPersonnel());

            // 数据质量
            setCellValue(row.createCell(8), monitoringRecord.getDataQuality());

            // 备注
            setCellValue(row.createCell(9), monitoringRecord.getRemark());
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        log.info("测量记录表创建完成，共导出 {} 条数据", monitoringRecordList.size());
    }

    /**
     * 创建影像信息表工作表
     */
    private void createImageSheet(Workbook workbook, List<Integer> regionIds) {
        Sheet sheet = workbook.createSheet(SHEET_IMAGE);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"序号", "采样点号", "影像获取时间", "传感器类型", "影像分辨率",
                           "影像路径", "影像描述", "备注"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            CellStyle headerStyle = createHeaderStyle(workbook);
            cell.setCellStyle(headerStyle);
        }

        // 查询数据
        List<TImage> imageList;
        if (regionIds != null && !regionIds.isEmpty()) {
            imageList = imageService.selectByRegionIds(regionIds);
        } else {
            imageList = imageService.selectAll();
        }

        // 填充数据
        int rowIndex = 1;
        int sequenceNumber = 1;
        for (TImage image : imageList) {
            Row row = sheet.createRow(rowIndex++);

            // 序号
            setCellValue(row.createCell(0), String.valueOf(sequenceNumber++));

            // 采样点号
            setCellValue(row.createCell(1), image.getPointCode());

            // 影像获取时间（完整的日期时间格式）
            if (image.getAcquisitionTime() != null) {
                setCellValue(row.createCell(2), image.getAcquisitionTime().format(DATETIME_FORMATTER));
            } else {
                setCellValue(row.createCell(2), "");
            }

            // 传感器类型
            setCellValue(row.createCell(3), image.getSensorType());

            // 影像分辨率
            setCellValue(row.createCell(4), image.getImageResolution());

            // 影像路径
            setCellValue(row.createCell(5), image.getImagePath());

            // 影像描述
            setCellValue(row.createCell(6), image.getImageDescription());

            // 备注
            setCellValue(row.createCell(7), image.getRemark());
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        log.info("影像信息表创建完成，共导出 {} 条数据", imageList.size());
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置背景色
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置字体
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        return style;
    }

    /**
     * 设置单元格值（处理null值）
     */
    private void setCellValue(Cell cell, String value) {
        if (value != null && !value.trim().isEmpty()) {
            cell.setCellValue(value);
        } else {
            cell.setCellValue("");
        }
    }

    /**
     * 格式化Double值
     */
    private String formatDoubleValue(Double value) {
        if (value == null) {
            return "";
        }
        // 保留适当的小数位数，去除不必要的零
        if (value == value.intValue()) {
            return String.valueOf(value.intValue());
        } else {
            return String.format("%.6f", value).replaceAll("0+$", "").replaceAll("\\.$", "");
        }
    }

    /**
     * 解析光谱数据的波长列
     */
    private String[] parseWavelengthColumns(String spectrumJson) {
        if (spectrumJson == null || spectrumJson.trim().isEmpty()) {
            return new String[0];
        }

        try {
            // 简单的JSON解析，提取波长键
            // 假设JSON格式为 {"400": 0.123, "450": 0.234, ...}
            Map<String, Double> spectrumData = parseSpectrumData(spectrumJson);
            return spectrumData.keySet().stream()
                    .sorted((a, b) -> Integer.compare(Integer.parseInt(a), Integer.parseInt(b)))
                    .toArray(String[]::new);
        } catch (Exception e) {
            log.warn("解析光谱数据波长列失败: {}", e.getMessage());
            return new String[0];
        }
    }

    /**
     * 解析光谱数据
     */
    private Map<String, Double> parseSpectrumData(String spectrumJson) {
        Map<String, Double> result = new java.util.HashMap<>();

        if (spectrumJson == null || spectrumJson.trim().isEmpty()) {
            return result;
        }

        try {
            // 简单的JSON解析实现
            // 移除大括号和空格
            String content = spectrumJson.trim().replaceAll("[{}\\s]", "");
            if (content.isEmpty()) {
                return result;
            }

            // 按逗号分割键值对
            String[] pairs = content.split(",");
            for (String pair : pairs) {
                String[] keyValue = pair.split(":");
                if (keyValue.length == 2) {
                    String key = keyValue[0].replaceAll("\"", "").trim();
                    String value = keyValue[1].replaceAll("\"", "").trim();
                    try {
                        result.put(key, Double.parseDouble(value));
                    } catch (NumberFormatException e) {
                        log.warn("解析光谱数据值失败: {} = {}", key, value);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析光谱数据失败: {}", e.getMessage());
        }

        return result;
    }
}
