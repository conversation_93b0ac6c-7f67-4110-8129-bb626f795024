package com.lysjk.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lysjk.constant.RoleConstant;
import com.lysjk.dto.TImagePageQueryDTO;
import com.lysjk.entity.TImage;
import com.lysjk.exception.AuthenticationException;
import com.lysjk.exception.BusinessException;
import com.lysjk.exception.ValidationException;
import com.lysjk.mapper.TImageMapper;
import com.lysjk.result.PageResult;
import com.lysjk.service.TImageService;
import com.lysjk.utils.DateTimeUtil;
import com.lysjk.utils.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 影像信息服务实现类
 */
@Slf4j
@Service
public class TImageServiceImpl implements TImageService {

    @Autowired
    private TImageMapper tImageMapper;

    /**
     * 新增影像信息
     * @param image 影像信息
     */
    @Override
    public void save(TImage image) {
        if (image == null) {
            throw new ValidationException.ParameterNullException("影像信息");
        }

        // 处理获取时间（如果未设置则使用当前时间）
        image.setAcquisitionDt(DateTimeUtil.processAcquisitionDateTime(image.getAcquisitionDt()));

        log.info("新增影像信息: name={}, sensorType={}, regionId={}, acquisitionDt={}",
                image.getName(), image.getSensorType(), image.getRegionId(), image.getAcquisitionDt());

        int result = tImageMapper.insertSelective(image);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("新增影像信息");
        }
    }

    /**
     * 批量删除影像信息
     * @param ids ID列表
     */
    @Override
    public void deleteBatch(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new ValidationException.ParameterNullException("影像ID列表");
        }

        // 权限检查：只有管理员可以删除
        Map<String, Object> claims = ThreadLocalUtil.get();
        if (claims == null) {
            throw new AuthenticationException.InsufficientPermissionException();
        }

        String role = (String) claims.get("role");
        if (!RoleConstant.ADMIN.equals(role)) {
            throw new AuthenticationException.InsufficientPermissionException();
        }

        log.info("批量删除影像信息: ids={}", ids);

        int result = tImageMapper.deleteByIds(ids);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("删除影像信息");
        }

        log.info("成功删除{}条影像信息", result);
    }

    /**
     * 更新影像信息
     * @param image 影像信息
     */
    @Override
    public void update(TImage image) {
        if (image == null) {
            throw new ValidationException.ParameterNullException("影像信息");
        }

        if (image.getId() == null) {
            throw new ValidationException.ParameterNullException("影像ID");
        }

        // 设置更新信息
        setUpdateInfo(image);

        log.info("更新影像信息: id={}, name={}, sensorType={}", 
                image.getId(), image.getName(), image.getSensorType());

        int result = tImageMapper.updateBusinessFields(image);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("更新影像信息");
        }
    }

    @Override
    public TImage selectByPrimaryKey(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("影像ID不能为空");
        }
        return tImageMapper.selectByPrimaryKey(id);
    }

    @Override
    public PageResult selectPage(TImagePageQueryDTO pageQueryDTO) {
        if (pageQueryDTO == null) {
            throw new ValidationException.ParameterNullException("分页查询条件");
        }

        log.info("分页查询影像信息: page={}, pageSize={}, name={}, sensorType={}, regionId={}",
                pageQueryDTO.getPage(), pageQueryDTO.getPageSize(),
                pageQueryDTO.getName(), pageQueryDTO.getSensorType(), pageQueryDTO.getRegionId());

        // 开启分页
        PageHelper.startPage(pageQueryDTO.getPage(), pageQueryDTO.getPageSize());

        // 执行查询
        Page<TImage> page = tImageMapper.pageQuery(pageQueryDTO);

        // 封装结果
        long total = page.getTotal();
        List<TImage> result = page.getResult();

        log.info("分页查询完成，共查询到{}条记录", total);
        return new PageResult(total, result);
    }

    @Override
    public List<TImage> selectByRegionId(Integer regionId) {
        if (regionId == null) {
            throw new ValidationException.ParameterNullException("地物ID");
        }
        
        log.info("根据地物ID查询影像信息: regionId={}", regionId);
        List<TImage> result = tImageMapper.selectByRegionId(regionId);
        log.info("查询完成，共查询到{}条影像信息", result != null ? result.size() : 0);
        return result;
    }

    @Override
    public List<TImage> selectBySensorType(String sensorType) {
        if (sensorType == null || sensorType.trim().isEmpty()) {
            throw new ValidationException.ParameterNullException("传感器类型");
        }
        
        log.info("根据传感器类型查询影像信息: sensorType={}", sensorType);
        List<TImage> result = tImageMapper.selectBySensorType(sensorType);
        log.info("查询完成，共查询到{}条影像信息", result != null ? result.size() : 0);
        return result;
    }

    /**
     * 批量新增影像信息
     * @param imageList 影像信息列表
     */
    @Override
    public void batchInsert(List<TImage> imageList) {
        if (imageList == null || imageList.isEmpty()) {
            throw new ValidationException.ParameterNullException("影像信息列表");
        }

        log.info("批量新增影像信息，数量: {}", imageList.size());

        // 处理获取时间
        for (TImage image : imageList) {
            if (image.getAcquisitionDt() != null) {
                image.setAcquisitionDt(DateTimeUtil.processAcquisitionDateTime(image.getAcquisitionDt()));
            }
        }

        int result = tImageMapper.batchInsert(imageList);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("批量新增影像信息");
        }

        log.info("批量新增影像信息成功，实际插入数量: {}", result);
    }

    @Override
    public List<TImage> selectByRegionIds(List<Integer> regionIds) {
        if (regionIds == null || regionIds.isEmpty()) {
            log.warn("地物ID列表为空，返回空列表");
            return new ArrayList<>();
        }
        log.info("根据地物ID列表查询影像信息: {}", regionIds);
        List<TImage> result = tImageMapper.selectByRegionIds(regionIds);
        log.info("查询完成，共查询到{}条影像信息", result != null ? result.size() : 0);
        return result;
    }

    @Override
    public List<TImage> selectAll() {
        log.info("查询所有影像信息");
        List<TImage> result = tImageMapper.selectAll();
        log.info("查询完成，共查询到{}条影像信息", result != null ? result.size() : 0);
        return result;
    }

    /**
     * 设置更新信息
     */
    private void setUpdateInfo(TImage record) {
        record.setUpdateDt(LocalDateTime.now());

        try {
            Map<String, Object> claims = ThreadLocalUtil.get();
            if (claims != null) {
                Integer userId = (Integer) claims.get("uid");
                record.setUpdateBy(userId);
            }
        } catch (Exception e) {
            log.warn("获取当前用户信息失败，使用默认值", e);
        }
    }
}
