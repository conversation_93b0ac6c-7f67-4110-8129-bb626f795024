package com.lysjk.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lysjk.constant.RoleConstant;
import com.lysjk.dto.TMonitoringRecordPageQueryDTO;
import com.lysjk.entity.TMonitoringRecord;
import com.lysjk.exception.AuthenticationException;
import com.lysjk.exception.BusinessException;
import com.lysjk.exception.ValidationException;
import com.lysjk.mapper.TMonitoringRecordMapper;
import com.lysjk.result.PageResult;
import com.lysjk.service.TMonitoringRecordService;
import com.lysjk.utils.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 测量记录服务实现类
 */
@Slf4j
@Service
@Transactional
public class TMonitoringRecordServiceImpl implements TMonitoringRecordService {

    @Autowired
    private TMonitoringRecordMapper monitoringRecordMapper;

    /**
     * 新增测量记录
     * @param monitoringRecord 测量记录信息
     */
    @Override
    public void save(TMonitoringRecord monitoringRecord) {
        if (monitoringRecord == null) {
            throw new ValidationException.ParameterNullException("测量记录信息");
        }

        log.info("新增测量记录: pointId={}, pointMeasure={}",
                monitoringRecord.getPointId(), monitoringRecord.getPointMeasure());

        int result = monitoringRecordMapper.insert(monitoringRecord);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("新增测量记录");
        }
    }

    /**
     * 批量删除测量记录
     * @param ids ID列表
     */
    @Override
    public void deleteBatch(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new ValidationException.ParameterNullException("删除ID列表");
        }

        // 权限验证
        Map<String, Object> claims = ThreadLocalUtil.get();
        String role = (String) claims.get(RoleConstant.ROLE);
        if (!RoleConstant.ADMIN.equals(role)) {
            throw new AuthenticationException.InsufficientPermissionException("删除测量记录");
        }

        log.info("批量删除测量记录: {}", ids);

        int result = monitoringRecordMapper.deleteByIds(ids);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("删除测量记录");
        }
    }

    /**
     * 更新测量记录
     * @param monitoringRecord 测量记录信息
     */
    @Override
    public void update(TMonitoringRecord monitoringRecord) {
        if (monitoringRecord == null) {
            throw new ValidationException.ParameterNullException("测量记录信息");
        }

        if (monitoringRecord.getId() == null) {
            throw new ValidationException.ParameterNullException("测量记录ID");
        }

        log.info("更新测量记录: id={}, pointId={}",
                monitoringRecord.getId(), monitoringRecord.getPointId());

        int result = monitoringRecordMapper.update(monitoringRecord);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("更新测量记录");
        }
    }

    /**
     * 分页查询测量记录
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    @Override
    public PageResult selectPage(TMonitoringRecordPageQueryDTO pageQueryDTO) {
        if (pageQueryDTO == null) {
            throw new ValidationException.ParameterNullException("分页查询条件");
        }

        log.info("分页查询测量记录: page={}, pageSize={}, pointId={}",
                pageQueryDTO.getPage(), pageQueryDTO.getPageSize(), pageQueryDTO.getPointId());

        // 开启分页
        PageHelper.startPage(pageQueryDTO.getPage(), pageQueryDTO.getPageSize());

        // 执行查询
        Page<TMonitoringRecord> page = monitoringRecordMapper.pageQuery(pageQueryDTO);

        // 封装结果
        long total = page.getTotal();
        List<TMonitoringRecord> result = page.getResult();

        log.info("分页查询完成，共查询到{}条记录", total);
        return new PageResult(total, result);
    }

    /**
     * 批量新增测量记录
     * @param monitoringRecordList 测量记录列表
     */
    @Override
    public void batchInsert(List<TMonitoringRecord> monitoringRecordList) {
        if (monitoringRecordList == null || monitoringRecordList.isEmpty()) {
            throw new ValidationException.ParameterNullException("测量记录列表");
        }

        log.info("批量新增测量记录，数量: {}", monitoringRecordList.size());

        int result = monitoringRecordMapper.batchInsert(monitoringRecordList);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("批量新增测量记录");
        }

        log.info("批量新增测量记录成功，实际插入数量: {}", result);
    }

    @Override
    public List<TMonitoringRecord> selectByRegionIds(List<Integer> regionIds) {
        return List.of();
    }

    @Override
    public List<TMonitoringRecord> selectAll() {
        return List.of();
    }
}
