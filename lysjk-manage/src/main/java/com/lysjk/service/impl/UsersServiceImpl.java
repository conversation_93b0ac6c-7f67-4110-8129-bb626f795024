package com.lysjk.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lysjk.constant.PasswordConstant;
import com.lysjk.constant.RoleConstant;
import com.lysjk.vo.LoginInfoVO;
import com.lysjk.entity.Users;
import com.lysjk.exception.CustomException;
import com.lysjk.mapper.UsersMapper;
import com.lysjk.service.UsersService;
import com.lysjk.utils.BCryptUtil;
import com.lysjk.utils.JwtUtil;
import com.lysjk.vo.UsersVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class UsersServiceImpl implements UsersService {
    @Autowired
    private UsersMapper usersMapper;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public Users selectByUsername(String username) {
        return usersMapper.selectByUsername(username);
    }

    public void createUser(String username, String password, String nickname, String defaultPro, Integer organizationId) {
        usersMapper.createUser(username, password, nickname, defaultPro, organizationId);
    }

    @Override
    public void register(String username, String password, String nickname, String defaultPro, Integer organizationId) {
        String encrypt = BCryptUtil.encrypt(password);
        this.createUser(username, encrypt, nickname, defaultPro, organizationId);
    }

    @Override
    public LoginInfoVO login(Users users) {
        // 1.根据员工用户名和密码查询员工信息(用户名具有唯一性,所以不需要列表)
        Users usersQuery = this.selectByUsername(users.getUsername());

        // 2.判断是否存在员工,存在就封装
        if (usersQuery != null) {
            if (BCryptUtil.verify(users.getPassword(), usersQuery.getPassword())) {
                log.info("登录成功,用户信息:{}", usersQuery);
                usersMapper.loginTime(usersQuery.getUid());
                // 生成JWT令牌
                Map<String, Object> claims = new HashMap<>();
                claims.put("uid", usersQuery.getUid()); // 必须存
                claims.put("username", usersQuery.getUsername());
                claims.put("role", usersQuery.getRole());
                String token = JwtUtil.genToken(claims);
                // 第一步登录向redis存token,因为当获取到token中也要注入到redis中才能进行校验
                Integer tokenId = usersQuery.getUid();
                ValueOperations<String, String> operations = stringRedisTemplate.opsForValue();
                operations.set(tokenId.toString(), token,72, TimeUnit.HOURS); // 先和token的过期时间保持一致
                return new LoginInfoVO(usersQuery.getUid(), usersQuery.getUsername(), usersQuery.getNickname(), usersQuery.getRole(),token);
            }
        }

        // 3.不存在就返回null
        return null;
    }

    @Override
    public void update(Users users) {
        usersMapper.update(users);
    }

//    @Override
//    public void updatePwd(String encrypt, Integer uid) {
//        usersMapper.updatePwd(encrypt, uid);
//    }

    @Override
    public void updatePwdByUid(Integer uid, String encryptPwd) {
        usersMapper.updatePwdByUid(uid, encryptPwd);
    }

    @Override
    public void deleteUser(Integer uid) {
        usersMapper.deleteUser(uid);
    }

    @Override
    public List<Users> selectAll() {
        return usersMapper.selectAll();
    }

    public PageInfo<Users> selectPage(Users users, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Users> list = usersMapper.selectAll();
        return PageInfo.of(list); // 固定写法
    }

    // 管理员可以随意更新用户信息
    @Override
    public void updateUser(Users users) {
        Users queryUsers = usersMapper.selectByUid(users.getUid());
        if (queryUsers != null) {
            // 传递过来的才有信息,queryUser没有信息
            usersMapper.update(users);
        }
        else
            throw new CustomException(404, "用户不存在");
    }

    @Override
    public List<Users> selectByUsernameLike(String username) {
        return usersMapper.selectByUsernameLike(username);
    }

    @Override
    public List<UsersVO> list(String username, String nickname) {
        return usersMapper.list(username, nickname);
    }

    @Override
    public void saveBatch(List<UsersVO> usersVOList) {
        // TODO: 可以再加上对是否重复的校验
        usersMapper.saveBatch(usersVOList, PasswordConstant.DEFAULT_PASSWORD_ENCRYPT, RoleConstant.USER);
    }

    @Override
    public void batchInsert(List<Users> usersList) {
        if (usersList == null || usersList.isEmpty()) {
            throw new CustomException(400, "用户信息列表不能为空");
        }

        log.info("批量新增用户信息，数量: {}", usersList.size());

        // 对密码进行加密处理
        for (Users user : usersList) {
            if (user.getPassword() != null && !user.getPassword().isEmpty()) {
                user.setPassword(BCryptUtil.encrypt(user.getPassword()));
            } else {
                // 如果没有设置密码，使用默认密码
                user.setPassword(PasswordConstant.DEFAULT_PASSWORD_ENCRYPT);
            }

            // 如果没有设置角色，使用默认角色
            if (user.getRole() == null || user.getRole().isEmpty()) {
                user.setRole(RoleConstant.USER);
            }
        }

        int result = usersMapper.batchInsert(usersList);
        if (result <= 0) {
            throw new CustomException(500, "批量新增用户信息失败");
        }

        log.info("批量新增用户信息成功，实际插入数量: {}", result);
    }
}
