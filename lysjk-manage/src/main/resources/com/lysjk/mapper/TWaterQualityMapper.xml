<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lysjk.mapper.TWaterQualityMapper">

  <!-- 基础结果映射 -->
  <resultMap id="BaseResultMap" type="com.lysjk.entity.TWaterQuality">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="point_id" jdbcType="INTEGER" property="pointId" />
    <result column="region_id" jdbcType="INTEGER" property="regionId" />
    <result column="chlorophyll" jdbcType="REAL" property="chlorophyll" />
    <result column="tss_organic" jdbcType="REAL" property="tssOrganic" />
    <result column="tss_inorganic" jdbcType="REAL" property="tssInorganic" />
    <result column="tss" jdbcType="REAL" property="tss" />
    <result column="transparency" jdbcType="REAL" property="transparency" />
    <result column="turbidity" jdbcType="REAL" property="turbidity" />
    <result column="water_temperature" jdbcType="REAL" property="waterTemperature" />
    <result column="ph" jdbcType="REAL" property="ph" />
    <result column="tn" jdbcType="REAL" property="tn" />
    <result column="tp" jdbcType="REAL" property="tp" />
    <result column="specific_conductance" jdbcType="REAL" property="specificConductance" />
    <result column="dissolved_oxyg" jdbcType="REAL" property="dissolvedOxyg" />
    <result column="permanganate_index" jdbcType="REAL" property="permanganateIndex" />
    <result column="ammonia_nitrogen" jdbcType="REAL" property="ammoniaNitrogen" />
    <result column="nitrite_nitrogen" jdbcType="REAL" property="nitriteNitrogen" />
    <result column="nitrate_nitrogen" jdbcType="REAL" property="nitrateNitrogen" />
    <result column="cdom400" jdbcType="REAL" property="cdom400" />
    <result column="cdom440" jdbcType="REAL" property="cdom440" />
    <result column="cdom480" jdbcType="REAL" property="cdom480" />
    <result column="rrs689675" jdbcType="REAL" property="rrs689675" />
    <result column="fluorinion" jdbcType="REAL" property="fluorinion" />
    <result column="chloridion" jdbcType="REAL" property="chloridion" />
    <result column="sulfate_ion" jdbcType="REAL" property="sulfateIon" />
    <result column="orgnization_id" jdbcType="INTEGER" property="orgnizationId" />
    <result column="monitoring_dt" jdbcType="TIMESTAMP" property="monitoringDt" />
    <result column="extension" jdbcType="OTHER" property="extension" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_dt" jdbcType="TIMESTAMP" property="createDt" />
    <result column="create_by" jdbcType="INTEGER" property="createBy" />
    <result column="update_dt" jdbcType="TIMESTAMP" property="updateDt" />
    <result column="update_by" jdbcType="INTEGER" property="updateBy" />
    <result column="yellow_substance" jdbcType="REAL" property="yellowSubstance" />
  </resultMap>

  <!-- 基础字段列表 -->
  <sql id="Base_Column_List">
    id, point_id, region_id, chlorophyll, tss_organic, tss_inorganic, tss, transparency,
    turbidity, water_temperature, ph, tn, tp, specific_conductance, dissolved_oxyg,
    permanganate_index, ammonia_nitrogen, nitrite_nitrogen, nitrate_nitrogen, cdom400,
    cdom440, cdom480, rrs689675, fluorinion, chloridion, sulfate_ion, orgnization_id,
    monitoring_dt, extension::text as extension, remark, create_dt, create_by, update_dt, update_by, yellow_substance
  </sql>

  <!-- 新增水质信息 -->
  <insert id="insert" useGeneratedKeys="true" keyProperty="id">
    insert into t_water_quality
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pointId != null">point_id,</if>
      <if test="regionId != null">region_id,</if>
      <if test="chlorophyll != null">chlorophyll,</if>
      <if test="tssOrganic != null">tss_organic,</if>
      <if test="tssInorganic != null">tss_inorganic,</if>
      <if test="tss != null">tss,</if>
      <if test="transparency != null">transparency,</if>
      <if test="turbidity != null">turbidity,</if>
      <if test="waterTemperature != null">water_temperature,</if>
      <if test="ph != null">ph,</if>
      <if test="tn != null">tn,</if>
      <if test="tp != null">tp,</if>
      <if test="specificConductance != null">specific_conductance,</if>
      <if test="dissolvedOxyg != null">dissolved_oxyg,</if>
      <if test="permanganateIndex != null">permanganate_index,</if>
      <if test="ammoniaNitrogen != null">ammonia_nitrogen,</if>
      <if test="nitriteNitrogen != null">nitrite_nitrogen,</if>
      <if test="nitrateNitrogen != null">nitrate_nitrogen,</if>
      <if test="cdom400 != null">cdom400,</if>
      <if test="cdom440 != null">cdom440,</if>
      <if test="cdom480 != null">cdom480,</if>
      <if test="rrs689675 != null">rrs689675,</if>
      <if test="fluorinion != null">fluorinion,</if>
      <if test="chloridion != null">chloridion,</if>
      <if test="sulfateIon != null">sulfate_ion,</if>
      <if test="orgnizationId != null">orgnization_id,</if>
      <if test="monitoringDt != null">monitoring_dt,</if>
      <if test="extension != null">extension,</if>
      <if test="remark != null and remark != ''">remark,</if>
      <if test="createDt != null">create_dt,</if>
      <if test="createBy != null">create_by,</if>
      <if test="updateDt != null">update_dt,</if>
      <if test="updateBy != null">update_by,</if>
      <if test="yellowSubstance != null">yellow_substance,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pointId != null">#{pointId},</if>
      <if test="regionId != null">#{regionId},</if>
      <if test="chlorophyll != null">#{chlorophyll},</if>
      <if test="tssOrganic != null">#{tssOrganic},</if>
      <if test="tssInorganic != null">#{tssInorganic},</if>
      <if test="tss != null">#{tss},</if>
      <if test="transparency != null">#{transparency},</if>
      <if test="turbidity != null">#{turbidity},</if>
      <if test="waterTemperature != null">#{waterTemperature},</if>
      <if test="ph != null">#{ph},</if>
      <if test="tn != null">#{tn},</if>
      <if test="tp != null">#{tp},</if>
      <if test="specificConductance != null">#{specificConductance},</if>
      <if test="dissolvedOxyg != null">#{dissolvedOxyg},</if>
      <if test="permanganateIndex != null">#{permanganateIndex},</if>
      <if test="ammoniaNitrogen != null">#{ammoniaNitrogen},</if>
      <if test="nitriteNitrogen != null">#{nitriteNitrogen},</if>
      <if test="nitrateNitrogen != null">#{nitrateNitrogen},</if>
      <if test="cdom400 != null">#{cdom400},</if>
      <if test="cdom440 != null">#{cdom440},</if>
      <if test="cdom480 != null">#{cdom480},</if>
      <if test="rrs689675 != null">#{rrs689675},</if>
      <if test="fluorinion != null">#{fluorinion},</if>
      <if test="chloridion != null">#{chloridion},</if>
      <if test="sulfateIon != null">#{sulfateIon},</if>
      <if test="orgnizationId != null">#{orgnizationId},</if>
      <if test="monitoringDt != null">#{monitoringDt},</if>
      <if test="extension != null and extension != ''">#{extension}::json,</if>
      <if test="remark != null and remark != ''">#{remark},</if>
      <if test="createDt != null">#{createDt},</if>
      <if test="createBy != null">#{createBy},</if>
      <if test="updateDt != null">#{updateDt},</if>
      <if test="updateBy != null">#{updateBy},</if>
      <if test="yellowSubstance != null">#{yellowSubstance},</if>
    </trim>
  </insert>

  <!-- 批量删除水质信息 -->
  <delete id="deleteByIds">
    delete from t_water_quality where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <!-- 更新水质信息 -->
  <update id="update">
    update t_water_quality
    <set>
      <if test="pointId != null">point_id = #{pointId},</if>
      <if test="regionId != null">region_id = #{regionId},</if>
      <if test="chlorophyll != null">chlorophyll = #{chlorophyll},</if>
      <if test="tssOrganic != null">tss_organic = #{tssOrganic},</if>
      <if test="tssInorganic != null">tss_inorganic = #{tssInorganic},</if>
      <if test="tss != null">tss = #{tss},</if>
      <if test="transparency != null">transparency = #{transparency},</if>
      <if test="turbidity != null">turbidity = #{turbidity},</if>
      <if test="waterTemperature != null">water_temperature = #{waterTemperature},</if>
      <if test="ph != null">ph = #{ph},</if>
      <if test="tn != null">tn = #{tn},</if>
      <if test="tp != null">tp = #{tp},</if>
      <if test="specificConductance != null">specific_conductance = #{specificConductance},</if>
      <if test="dissolvedOxyg != null">dissolved_oxyg = #{dissolvedOxyg},</if>
      <if test="permanganateIndex != null">permanganate_index = #{permanganateIndex},</if>
      <if test="ammoniaNitrogen != null">ammonia_nitrogen = #{ammoniaNitrogen},</if>
      <if test="nitriteNitrogen != null">nitrite_nitrogen = #{nitriteNitrogen},</if>
      <if test="nitrateNitrogen != null">nitrate_nitrogen = #{nitrateNitrogen},</if>
      <if test="cdom400 != null">cdom400 = #{cdom400},</if>
      <if test="cdom440 != null">cdom440 = #{cdom440},</if>
      <if test="cdom480 != null">cdom480 = #{cdom480},</if>
      <if test="rrs689675 != null">rrs689675 = #{rrs689675},</if>
      <if test="fluorinion != null">fluorinion = #{fluorinion},</if>
      <if test="chloridion != null">chloridion = #{chloridion},</if>
      <if test="sulfateIon != null">sulfate_ion = #{sulfateIon},</if>
      <if test="orgnizationId != null">orgnization_id = #{orgnizationId},</if>
      <if test="monitoringDt != null">monitoring_dt = #{monitoringDt},</if>
      <if test="extension != null and extension != ''">extension = #{extension}::json,</if>
      <if test="remark != null and remark != ''">remark = #{remark},</if>
      <if test="updateDt != null">update_dt = #{updateDt},</if>
      <if test="updateBy != null">update_by = #{updateBy},</if>
      <if test="yellowSubstance != null">yellow_substance = #{yellowSubstance},</if>
    </set>
    where id = #{id}
  </update>

  <!-- 分页查询水质信息 -->
  <select id="pageQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_water_quality
    <where>
      <if test="pointId != null">
        and point_id = #{pointId}
      </if>
      <if test="regionId != null">
        and region_id = #{regionId}
      </if>
      <if test="chlorophyll != null">
        and chlorophyll = #{chlorophyll}
      </if>
      <if test="tssOrganic != null">
        and tss_organic = #{tssOrganic}
      </if>
      <if test="tssInorganic != null">
        and tss_inorganic = #{tssInorganic}
      </if>
      <if test="tss != null">
        and tss = #{tss}
      </if>
      <if test="transparency != null">
        and transparency = #{transparency}
      </if>
      <if test="turbidity != null">
        and turbidity = #{turbidity}
      </if>
      <if test="waterTemperature != null">
        and water_temperature = #{waterTemperature}
      </if>
      <if test="ph != null">
        and ph = #{ph}
      </if>
      <if test="tn != null">
        and tn = #{tn}
      </if>
      <if test="tp != null">
        and tp = #{tp}
      </if>
      <if test="specificConductance != null">
        and specific_conductance = #{specificConductance}
      </if>
      <if test="dissolvedOxyg != null">
        and dissolved_oxyg = #{dissolvedOxyg}
      </if>
      <if test="permanganateIndex != null">
        and permanganate_index = #{permanganateIndex}
      </if>
      <if test="ammoniaNitrogen != null">
        and ammonia_nitrogen = #{ammoniaNitrogen}
      </if>
      <if test="nitriteNitrogen != null">
        and nitrite_nitrogen = #{nitriteNitrogen}
      </if>
      <if test="nitrateNitrogen != null">
        and nitrate_nitrogen = #{nitrateNitrogen}
      </if>
      <if test="cdom400 != null">
        and cdom400 = #{cdom400}
      </if>
      <if test="cdom440 != null">
        and cdom440 = #{cdom440}
      </if>
      <if test="cdom480 != null">
        and cdom480 = #{cdom480}
      </if>
      <if test="rrs689675 != null">
        and rrs689675 = #{rrs689675}
      </if>
      <if test="fluorinion != null">
        and fluorinion = #{fluorinion}
      </if>
      <if test="chloridion != null">
        and chloridion = #{chloridion}
      </if>
      <if test="sulfateIon != null">
        and sulfate_ion = #{sulfateIon}
      </if>
      <if test="orgnizationId != null">
        and orgnization_id = #{orgnizationId}
      </if>
      <if test="extension != null and extension != ''">
        and extension::text like concat('%', #{extension}, '%')
      </if>
      <if test="yellowSubstance != null">
        and yellow_substance = #{yellowSubstance}
      </if>
      <if test="monitoringDt != null">
        and monitoring_dt = #{monitoringDt}
      </if>
    </where>
    order by monitoring_dt desc, create_dt desc
  </select>

  <!-- 批量新增水质信息 -->
  <insert id="batchInsert">
    insert into t_water_quality (point_id, region_id, chlorophyll, tss_organic, tss_inorganic, tss,
      transparency, turbidity, water_temperature, ph, tn, tp, specific_conductance, dissolved_oxyg,
      permanganate_index, ammonia_nitrogen, nitrite_nitrogen, nitrate_nitrogen, cdom400, cdom440,
      cdom480, rrs689675, fluorinion, chloridion, sulfate_ion, orgnization_id, monitoring_dt,
      extension, remark, create_dt, create_by, update_dt, update_by, yellow_substance)
    values
    <foreach collection="waterQualityList" item="item" separator=",">
      (#{item.pointId,jdbcType=INTEGER}, #{item.regionId,jdbcType=INTEGER}, #{item.chlorophyll,jdbcType=REAL},
       #{item.tssOrganic,jdbcType=REAL}, #{item.tssInorganic,jdbcType=REAL}, #{item.tss,jdbcType=REAL},
       #{item.transparency,jdbcType=REAL}, #{item.turbidity,jdbcType=REAL}, #{item.waterTemperature,jdbcType=REAL},
       #{item.ph,jdbcType=REAL}, #{item.tn,jdbcType=REAL}, #{item.tp,jdbcType=REAL},
       #{item.specificConductance,jdbcType=REAL}, #{item.dissolvedOxyg,jdbcType=REAL},
       #{item.permanganateIndex,jdbcType=REAL}, #{item.ammoniaNitrogen,jdbcType=REAL},
       #{item.nitriteNitrogen,jdbcType=REAL}, #{item.nitrateNitrogen,jdbcType=REAL},
       #{item.cdom400,jdbcType=REAL}, #{item.cdom440,jdbcType=REAL}, #{item.cdom480,jdbcType=REAL},
       #{item.rrs689675,jdbcType=REAL}, #{item.fluorinion,jdbcType=REAL}, #{item.chloridion,jdbcType=REAL},
       #{item.sulfateIon,jdbcType=REAL}, #{item.orgnizationId,jdbcType=INTEGER}, #{item.monitoringDt,jdbcType=TIMESTAMP},
       <choose>
         <when test="item.extension != null and item.extension != ''">#{item.extension,jdbcType=OTHER}::json</when>
         <otherwise>null</otherwise>
       </choose>,
       #{item.remark,jdbcType=VARCHAR}, #{item.createDt,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=INTEGER},
       #{item.updateDt,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=INTEGER}, #{item.yellowSubstance,jdbcType=REAL})
    </foreach>
  </insert>

</mapper>
